# 到店自提状态显示修复

## 问题描述

用户反馈：订单数据显示 `deliveryStatus: 30`，但页面显示"备餐中"状态并显示"备餐完成"按钮，这是不正确的。

## 问题分析

根据后端数据：
```json
{
  "orderStatus": 30,        // OrderStatus.PROCESSING (处理中)
  "deliveryStatus": 30,     // DeliveryStatus.PICKING (取餐中)
  "statusText": "已接单待取货"
}
```

### 状态映射问题

**修复前的错误映射：**
- `DeliveryStatus.PICKING (30)` → "备餐中" ❌
- 显示"备餐完成"按钮 ❌

**修复后的正确映射：**
- `DeliveryStatus.PICKING (30)` → "等待自提" ✅
- 显示"确认自提"按钮 ✅

## 修复内容

### 1. 状态文本映射修复

```typescript
// 修复前
const getPickupStatusText = (deliveryStatus: DeliveryStatus) => {
  const statusMap: Record<DeliveryStatus, string> = {
    [DeliveryStatus.PICKING]: '备餐中',  // ❌ 错误
    // ...
  };
};

// 修复后
const getPickupStatusText = (deliveryStatus: DeliveryStatus) => {
  const statusMap: Record<DeliveryStatus, string> = {
    [DeliveryStatus.PICKING]: '等待自提',  // ✅ 正确
    // ...
  };
};
```

### 2. 状态标签类型修复

```typescript
// 修复后
const getPickupStatusTagType = (deliveryStatus: DeliveryStatus) => {
  const typeMap: Record<DeliveryStatus, string> = {
    [DeliveryStatus.PICKING]: 'primary',  // 等待自提用primary标签
    // ...
  };
};
```

### 3. 操作按钮判断逻辑修复

```typescript
// 修复备餐完成按钮判断
const canCompletePickupPreparation = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  // 只有在ACCEPTED(20)状态时才显示备餐完成按钮
  // PICKING(30)状态表示已经备餐完成，不应该再显示此按钮
  return status === OrderStatus.PROCESSING && deliveryStatusNum === DeliveryStatus.ACCEPTED;
};

// 修复确认自提按钮判断
const canCompletePickup = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  // PICKING(30)状态时应该显示确认自提按钮
  return (status === OrderStatus.PROCESSING || status === OrderStatus.DELIVERING) && (
    deliveryStatusNum === DeliveryStatus.PICKING ||      // 30-取餐中状态，等待自提
    deliveryStatusNum === DeliveryStatus.PICKED_UP ||
    deliveryStatusNum === DeliveryStatus.DELIVERING
  );
};
```

## 状态流转逻辑

### 到店自提订单的正确状态流转：

1. **下单** → `orderStatus: 10` (待支付)
2. **支付** → `orderStatus: 20` (已支付)
3. **接单** → `orderStatus: 30, deliveryStatus: 20` (处理中-已接单)
   - 显示状态："备餐中"
   - 显示按钮："备餐完成"
4. **备餐完成** → `orderStatus: 30, deliveryStatus: 30` (处理中-取餐中)
   - 显示状态："等待自提" ✅
   - 显示按钮："确认自提" ✅
5. **确认自提** → `orderStatus: 50, deliveryStatus: 60` (已完成-已送达)
   - 显示状态："自提完成"

## 验证方法

使用提供的测试数据：
```json
{
  "orderStatus": 30,
  "deliveryStatus": 30,
  "deliveryType": 2,
  "statusText": "已接单待取货"
}
```

**预期结果：**
- 自提状态显示："等待自提"
- 状态标签类型：`primary` (蓝色)
- 显示按钮："确认自提"
- 不显示："备餐完成"按钮

## 影响范围

此修复只影响到店自提订单（`deliveryType: 2`）的状态显示和操作按钮，不影响其他配送方式的订单。

## 测试建议

1. 测试不同 `deliveryStatus` 值的状态显示
2. 验证操作按钮的正确显示/隐藏
3. 确认状态标签颜色正确
4. 测试确认自提功能的取餐码验证
