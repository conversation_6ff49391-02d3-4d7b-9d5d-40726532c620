/**
 * 商家模块路由配置
 * 提供静态路由配置和动态路由生成功能
 */
import MerchantLayout from '@/layouts/MerchantLayout.vue';
import type { RouteRecordRaw } from 'vue-router';

// 定义前端路径数据的接口
interface PathItem {
  path: string;
  title: string;
  count: number;
  config_key: string;
  config_type: string;
  group: string;
  icon: string;
  id: string;
  version_hash: string;
  [key: string]: any;
}

export interface ModuleData {
  module: string;
  paths: PathItem[];
}

// 提前导入并缓存组件，避免懒加载问题
// 这将确保组件在路由初始化时就已经开始加载
const DynamicConfigPagePromise = import('@/modules/admin/views/dynamicGridConfig/DynamicGridConfigPage.vue');

// 强制预加载组件，确保刷新页面时组件已就绪
DynamicConfigPagePromise.then(() => console.log('动态配置页面组件预加载完成'))
  .catch(err => console.error('动态配置页面组件预加载失败:', err));
  
// 使用一个函数返回已经开始加载的Promise
export const getDynamicConfigComponent = () => DynamicConfigPagePromise;

// 静态路由配置
const staticRoutes: RouteRecordRaw[] = [
  // 商家登录
  {
    path: '/merchant/login',
    name: 'MerchantLogin',
    component: () => import('../views/Login.vue'),
    meta: { title: '商家登录', requiresAuth: false }
  },
  // 商家入驻申请
  {
    path: '/merchant/apply',
    name: 'MerchantApply',
    component: () => import('../views/Apply.vue'),
    meta: { title: '商家入驻', requiresAuth: false }
  },
  // 商家状态页面（审核中/被拒绝/被暂停）- 独立页面
  {
    path: '/merchant/status',
    name: 'MerchantStatus',
    component: () => import('../views/Status.vue'),
    meta: { title: '入驻状态', requiresAuth: true }
  },
  {
    path: '/merchant',
    name: 'MerchantMain',
    component: MerchantLayout,
    redirect: '/merchant/dashboard',
    meta: { requiresAuth: true, requireApproved: true },
    children: [
      
      
      // 商家后台功能（需要审核通过）
      {
        path: 'dashboard',
        name: 'MerchantDashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '商家后台', requiresAuth: true,  requireApproved: true }
      },
      
      // 店铺管理
      {
        path: 'shop',
        name: 'MerchantShop',
        component: () => import('../views/Shop.vue'),
        meta: { title: '店铺管理', requiresAuth: true,  requireApproved: true }
      },
      
      // 外卖商品管理
      {
        path: 'takeout/food/list',
        name: 'MerchantTakeoutFoodList',
        component: () => import('../views/FoodList.vue'),
        meta: { title: '外卖商品列表', requiresAuth: true, requireApproved: true, icon: 'Food' }
      },
      {
        path: 'takeout/food/add',
        name: 'MerchantTakeoutFoodAdd',
        component: () => import('../views/FoodEdit.vue'),
        meta: { title: '新增外卖商品', requiresAuth: true, requireApproved: true, icon: 'Plus' }
      },
      {
        path: 'takeout/food/edit/:id',
        name: 'MerchantTakeoutFoodEdit',
        component: () => import('../views/FoodEdit.vue'),
        meta: { title: '编辑外卖商品', requiresAuth: true, requireApproved: true, icon: 'Edit' },
        props: true
      },
      
      // 外卖订单管理
      {
        path: 'order/list',
        name: 'MerchantOrderList',
        component: () => import('../views/order/OrderList.vue'),
        meta: { title: '外卖订单列表', requiresAuth: true, requireApproved: true, icon: 'List' }
      },
      {
        path: 'order/detail/:id',
        name: 'MerchantOrderDetail',
        component: () => import('../views/order/OrderDetail.vue'),
        meta: { title: '订单详情', requiresAuth: true, requireApproved: true, icon: 'Document' },
        props: true
      },
      {
        path: 'order/pickup-query',
        name: 'MerchantOrderPickupQuery',
        component: () => import('../views/order/PickupQuery.vue'),
        meta: { title: '取餐号查询', requiresAuth: true, requireApproved: true, icon: 'Search' }
      },
      {
        path: 'order/statistics',
        name: 'MerchantOrderStatistics',
        component: () => import('../views/order/OrderStatistics.vue'),
        meta: { title: '订单统计', requiresAuth: true, requireApproved: true, icon: 'DataAnalysis' }
      },

      // 外卖分类管理
      {
        path: 'takeout/category/list',
        name: 'MerchantTakeoutCategoryList',
        component: () => import('../views/CategoryList.vue'),
        meta: { title: '外卖分类管理', requiresAuth: true, requireApproved: true, icon: 'Menu' }
      },
      {
        path: 'takeout/category/add',
        name: 'MerchantTakeoutCategoryAdd',
        component: () => import('../views/CategoryEdit.vue'),
        meta: { title: '新增外卖分类', requiresAuth: true, requireApproved: true, icon: 'Plus' }
      },
      {
        path: 'takeout/category/edit/:id',
        name: 'MerchantTakeoutCategoryEdit',
        component: () => import('../views/CategoryEdit.vue'),
        meta: { title: '编辑外卖分类', requiresAuth: true, requireApproved: true, icon: 'Edit' },
        props: true
      },
      
      // 优惠券管理
      {
        path: 'coupon/list',
        name: 'MerchantCouponList',
        component: () => import('../views/CouponList.vue'),
        meta: { title: '优惠券管理', requiresAuth: true, requireApproved: true, icon: 'Discount' }
      },
      {
        path: 'coupon/add',
        name: 'MerchantCouponAdd',
        component: () => import('../views/CouponEdit.vue'),
        meta: { title: '新增优惠券', requiresAuth: true, requireApproved: true, icon: 'Plus' }
      },
      {
        path: 'coupon/edit/:id',
        name: 'MerchantCouponEdit',
        component: () => import('../views/CouponEdit.vue'),
        meta: { title: '编辑优惠券', requiresAuth: true, requireApproved: true, icon: 'Edit' },
        props: true
      },
      
      // 促销活动管理
      {
        path: 'promotion/list',
        name: 'MerchantPromotionList',
        component: () => import('../views/PromotionList.vue'),
        meta: { title: '促销活动管理', requiresAuth: true, requireApproved: true, icon: 'Promotion' }
      },
      {
        path: 'promotion/add',
        name: 'MerchantPromotionAdd',
        component: () => import('../views/PromotionEdit.vue'),
        meta: { title: '新增促销活动', requiresAuth: true, requireApproved: true, icon: 'Plus' }
      },
      {
        path: 'promotion/edit/:id',
        name: 'MerchantPromotionEdit',
        component: () => import('../views/PromotionEdit.vue'),
        meta: { title: '编辑促销活动', requiresAuth: true, requireApproved: true, icon: 'Edit' },
        props: true
      },
      
      // 账户设置
      {
        path: 'settings',
        name: 'MerchantSettings',
        component: () => import('../views/Settings.vue'),
        meta: { title: '账户设置', requiresAuth: true, }
      },
      
      // 动态配置页面 - 根据路径参数渲染不同配置
      {
        path: 'page/:configId',
        name: 'MerchantDynamicConfigPage',
        component: getDynamicConfigComponent,
        meta: {
          title: '动态配置页面',
          requiresAuth: true,
          //roles: ['merchant'],
          requireApproved: true,
          dynamic: true
        },
        props: true
      },
      
      // 营业时间管理
      {
        path: 'business-hours',
        name: 'MerchantBusinessHours',
        component: () => import('../views/business-hours/index.vue'),
        meta: { 
          title: '营业时间管理', 
          requiresAuth: true, 
          requireApproved: true,
          icon: 'Clock' 
        }
      }
    ]
  }
];

// 缓存已生成的动态路由配置
// 通过导出变量，确保可以从外部访问和修改缓存状态
export let cachedDynamicRoutes: RouteRecordRaw[] | null = null;

/**
 * 根据前端路径数据生成动态路由配置
 * @param frontendPaths 前端路径数据
 * @returns 动态生成的路由配置
 */
export function generateDynamicRoutes(frontendPaths: ModuleData[]): RouteRecordRaw[] {
  try {
    // 注释掉缓存逻辑，强制每次重新生成动态路由
    // 之前的缓存机制可能导致新路由不被添加
    // if (cachedDynamicRoutes) {
    //   console.log('使用缓存的动态路由配置');
    //   return cachedDynamicRoutes;
    // }
    
    // 每次都清除缓存，避免无限循环
    cachedDynamicRoutes = null;
    console.log('强制清除路由缓存，重新生成动态路由');
    
    
    console.log('生成动态路由配置，前端路径数据:', frontendPaths);
    
    // 验证前端路径数据
    if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
      console.warn('前端路径数据无效，使用静态路由');
      return staticRoutes;
    }
    
    // 记录新添加的路由路径
    const newRoutes: string[] = [];
    
    // 创建商家主路由的子路由副本，以便追加动态路由
    const merchantMainRouteIndex = staticRoutes.findIndex(route => route.path === '/merchant');
    if (merchantMainRouteIndex === -1) {
      console.warn('找不到商家主路由配置，无法添加动态子路由');
      return staticRoutes;
    }
    
    // 复制商家主路由的子路由数组
    const merchantMainRouteChildren = [...(staticRoutes[merchantMainRouteIndex].children || [])];
    
    // 处理前端路径数据中的每个模块
    frontendPaths.forEach(moduleData => {
      if (!moduleData || !moduleData.module || !Array.isArray(moduleData.paths)) {
        console.warn('模块数据格式无效，跳过:', moduleData);
        return;
      }
      
      const moduleName = moduleData.module;
      
      // 只处理merchant模块的路径
      switch (moduleName) {
        case 'merchant':
          console.log('处理merchant模块的动态路由');
          
          // 处理模块中的每个路径项
          console.log('商家模块所有路径项:', moduleData.paths.map(p => p.path));
          moduleData.paths.forEach(pathItem => {
            if (!pathItem || !pathItem.path || !pathItem.title) {
              console.warn('路径项数据格式无效，跳过:', pathItem);
              return;
            }
            
            // 构造完整路径和路由名称
            const modulePath = pathItem.path;
            const routeName = `Merchant${pathItem.path.split('/').map(p => p.charAt(0).toUpperCase() + p.slice(1)).join('')}`;
            
            // 检查路由是否已存在
            const existingRoute = merchantMainRouteChildren.find(route => route.path === modulePath);
            if (!existingRoute) {
              // 创建新的路由配置
              const newRoute: RouteRecordRaw = {
                path: modulePath,
                name: routeName,
                component: getDynamicConfigComponent,
                meta: {
                  title: pathItem.title,
                  icon: pathItem.icon || undefined,
                  requiresAuth: true,
                  roles: ['merchant'],
                  requireApproved: true,
                  dynamic: true,
                  configKey: pathItem.config_key,
                  configType: pathItem.config_type,
                  group: pathItem.group,
                  moduleData: {
                    module: moduleName,
                    path: pathItem.path,
                    id: pathItem.id,
                    versionHash: pathItem.version_hash,
                    count: pathItem.count || 0
                  }
                }
              };
              
              // 添加到商家子路由
              merchantMainRouteChildren.push(newRoute);
              
              // 记录完整路径
              const fullPath = `/merchant/${modulePath}`;
              newRoutes.push(fullPath);
              
              console.log(`已添加动态路由: ${fullPath}, 名称: ${routeName}`);
            } else {
              console.log(`路由已存在，跳过: /merchant/${modulePath}`);
            }
          });
          break;
      }
    });
    
    if (newRoutes.length > 0) {
      console.log(`动态路由生成完成，共添加 ${newRoutes.length} 个路由:`, newRoutes);
    } else {
      console.log('未添加任何新的动态路由');
    }
    
    // 创建新的静态路由深拷贝
    const newStaticRoutes = JSON.parse(JSON.stringify(staticRoutes));
    // 更新商家主路由的子路由
    const updatedMerchantRouteIndex = newStaticRoutes.findIndex((route: RouteRecordRaw) => route.path === '/merchant');
    if (updatedMerchantRouteIndex !== -1 && newStaticRoutes[updatedMerchantRouteIndex]) {
      newStaticRoutes[updatedMerchantRouteIndex].children = merchantMainRouteChildren;
    }
    
    // 重要：确保所有路由都有对应的组件引用
    newStaticRoutes.forEach((route: RouteRecordRaw) => {
      // 处理登录路由
      if (route.path === '/merchant/login' && !route.component) {
        route.component = () => import('../views/Login.vue');
      }
      
      // 处理商家主路由
      if (route.path === '/merchant') {
        if (!route.component) {
          route.component = MerchantLayout;
        }
        
        // 处理子路由
        if (route.children) {
          route.children.forEach((childRoute: RouteRecordRaw) => {
            if (!childRoute.component) {
              // 根据路径模式添加组件引用
              if (childRoute.meta?.dynamic || childRoute.path === 'page/:configId') {
                childRoute.component = getDynamicConfigComponent;
              } else {
                // 基于约定的路径命名模式导入组件
                const viewName = childRoute.path.split('/').pop();
                if (viewName) {
                  const capitalized = viewName.charAt(0).toUpperCase() + viewName.slice(1);
                  // 设置延迟加载的组件
                  childRoute.component = () => import(`../views/${capitalized}.vue`);
                }
              }
            }
          });
        }
      }
    });
    
    // 输出最终路由信息以便调试
    const routeDebugInfo = newStaticRoutes
      .filter((route: RouteRecordRaw) => route.path === '/merchant')[0]?.children
      ?.map((child: RouteRecordRaw) => ({
        path: child.path, 
        name: child.name,
        fullPath: `/merchant/${child.path}`,
        component: child.component ? 'Available' : 'Missing',
        meta: child.meta
      }));
    
    console.log('生成的所有子路由:', routeDebugInfo);
    
    // 只在有新路由时才缓存
    if (newRoutes.length > 0) {
      cachedDynamicRoutes = newStaticRoutes;
      console.log(`缓存动态路由配置，包含 ${newRoutes.length} 个新路由`);
    } else {
      // 结果实际未变，清空缓存以监测问题
      cachedDynamicRoutes = null; 
      console.log('未添加新路由，缓存未更新');
    }
    
    // 返回完整的路由配置（包含静态路由和新添加的动态路由）
    return newStaticRoutes;
  } catch (error) {
    console.error('生成动态路由时发生错误:', error);
    return staticRoutes;
  }
}

/**
 * 清除动态路由缓存
 * 在路由更新或需要重新生成时调用
 */
export function clearDynamicRoutesCache() {
  cachedDynamicRoutes = null;
  console.log('动态路由缓存已清除');
}

// 默认导出静态路由配置
export default staticRoutes;