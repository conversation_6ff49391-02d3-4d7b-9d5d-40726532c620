<!--
/**
 * 订单详情页面
 * <AUTHOR>
 * @date 2025-01-20
 * @version 1.0.0
 * @description 商家订单详情查看页面，支持订单信息展示、状态管理、配送操作、退款处理等功能
 */
-->
<template>
  <div class="order-detail-page">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </div>
      </template>

      <div v-if="order">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="订单号">{{ order.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="statusTagType(order.orderStatus)">{{ OrderStatusText[order.orderStatus as OrderStatus] || '未知状态' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ formatTime(order.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ order.payTime ? formatTime(order.payTime) : '未支付' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 用户信息暂不可用，可能需要额外接口获取 -->
        <el-descriptions title="用户信息" :column="2" border class="section-margin">
          <el-descriptions-item label="用户ID">{{ order.userID }}</el-descriptions-item>
          <el-descriptions-item label="商家ID">{{ order.merchantID }}</el-descriptions-item>
        </el-descriptions>

        <!-- 根据配送方式显示不同的信息 -->
        <el-descriptions v-if="order.deliveryType !== 2" title="收货信息" :column="1" border class="section-margin">
          <el-descriptions-item label="收货人姓名">
            {{ order.deliveryInfo?.receiverName || '暂无收货人姓名' }}
          </el-descriptions-item>
          <el-descriptions-item label="收货人电话">
            {{ order.deliveryInfo?.receiverPhone || '暂无收货人电话' }}
          </el-descriptions-item>
          <el-descriptions-item label="收货地址">
            {{ order.deliveryInfo?.deliveryAddress || '暂无收货地址' }}
          </el-descriptions-item>
          <el-descriptions-item label="配送距离">{{ order.deliveryInfo?.deliveryDistance || 0 }}km</el-descriptions-item>
          <el-descriptions-item label="预计送达时间">{{ order.deliveryInfo?.expectedTime || '暂无信息' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 到店自提信息 -->
        <el-descriptions v-if="order.deliveryType === 2" title="自提信息" :column="1" border class="section-margin">
          <el-descriptions-item label="配送方式">
            <el-tag type="info">到店自提</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="取餐码">
            <el-tag v-if="order.deliveryInfo?.pickupCode" type="primary" size="large">
              {{ order.deliveryInfo.pickupCode }}
            </el-tag>
            <span v-else>暂无取餐码</span>
          </el-descriptions-item>
          <el-descriptions-item label="取餐码状态">
            <el-tag :type="order.deliveryInfo?.pickupCodeUsed ? 'success' : 'warning'">
              {{ order.deliveryInfo?.pickupCodeUsed ? '已使用' : '未使用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="order.deliveryInfo?.pickupCodeUsedTime" label="使用时间">
            {{ formatTime(order.deliveryInfo.pickupCodeUsedTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="预计取餐时间">
            {{ order.deliveryInfo?.expectedPickupTime || order.deliveryInfo?.expectedTime || '暂无信息' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <h3 class="section-title">商品信息</h3>
        <el-table :data="order.items" border style="width: 100%">
          <el-table-column prop="productName" label="商品名称"></el-table-column>
          <el-table-column prop="specText" label="规格"></el-table-column>
          <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
          <el-table-column prop="price" label="单价" width="100">
            <template #default="scope">¥{{ scope.row.price.toFixed(2) }}</template>
          </el-table-column>
          <el-table-column label="小计" width="120">
            <template #default="scope">¥{{ (scope.row.quantity * scope.row.price).toFixed(2) }}</template>
          </el-table-column>
        </el-table>

        <el-descriptions title="金额信息" :column="2" border class="section-margin">
          <el-descriptions-item label="商品总额">¥{{ calculateItemsTotalAmount(order.items).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="配送费">¥{{ order.deliveryFee.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="包装费">¥{{ order.packagingFee.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="优惠金额">- ¥{{ order.discountAmount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="合计金额">
            <strong style="color: #f56c6c;">¥{{ order.totalAmount.toFixed(2) }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="实付金额">
            <strong style="color: #f56c6c;">¥{{ order.totalAmount.toFixed(2) }}</strong>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 跑腿员配送和商家自配送的配送信息 -->
        <el-descriptions v-if="order.deliveryType !== 2 && order.deliveryInfo && (order.deliveryInfo.deliveryStaffName || order.deliveryInfo.deliveryStatus)" title="配送信息" :column="2" border class="section-margin">
            <el-descriptions-item label="配送方式">
              <el-tag :type="getDeliveryTypeTagType(order.deliveryType)">
                {{ getDeliveryTypeText(order.deliveryType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="配送员">{{ order.deliveryInfo.deliveryStaffName || '暂未分配' }}</el-descriptions-item>
            <el-descriptions-item label="配送员电话">{{ order.deliveryInfo.deliveryStaffPhone || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="配送状态">{{ DeliveryStatusText[order.deliveryInfo.deliveryStatus as DeliveryStatus] || '未知状态' }}</el-descriptions-item>
            <el-descriptions-item v-if="order.deliveryInfo.estimatedArrivalTime" label="预计送达时间">{{ order.deliveryInfo.estimatedArrivalTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 到店自提的配送状态信息 -->
        <el-descriptions v-if="order.deliveryType === 2 && order.deliveryInfo" title="自提状态" :column="2" border class="section-margin">
            <el-descriptions-item label="配送方式">
              <el-tag type="info">到店自提</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="自提状态">
              <el-tag :type="getPickupStatusTagType(order.deliveryInfo.deliveryStatus)">
                {{ getPickupStatusText(order.deliveryInfo.deliveryStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="order.deliveryInfo.startTime" label="开始备餐时间">
              {{ formatTime(order.deliveryInfo.startTime) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="order.deliveryInfo.endTime" label="完成时间">
              {{ formatTime(order.deliveryInfo.endTime) }}
            </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="备注信息" :column="1" border class="section-margin">
          <el-descriptions-item label="订单备注">{{ order.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 退款信息 -->
        <el-descriptions v-if="order.refundInfo && (order.orderStatus === OrderStatus.REFUNDING || order.orderStatus === OrderStatus.REFUNDED)" title="退款信息" :column="2" border class="section-margin">
          <el-descriptions-item label="退款单号" v-if="order.refundInfo.refundNo">
            <el-tag type="info">{{ order.refundInfo.refundNo }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="退款金额">
            <strong style="color: #f56c6c;">¥{{ order.refundInfo.refundAmount.toFixed(2) }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="退款状态">
            <el-tag :type="getRefundStatusTagType(order.refundInfo.refundStatus)">{{ getRefundStatusText(order.refundInfo.refundStatus) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatTime(order.refundInfo.applyTime) }}</el-descriptions-item>
          <el-descriptions-item v-if="order.refundInfo.processTime" label="处理时间">{{ formatTime(order.refundInfo.processTime) }}</el-descriptions-item>
          <el-descriptions-item v-if="order.refundInfo.refundTime" label="退款时间">{{ formatTime(order.refundInfo.refundTime) }}</el-descriptions-item>
          <el-descriptions-item label="退款原因">{{ order.refundInfo.refundReason || '无' }}</el-descriptions-item>
          <el-descriptions-item v-if="order.refundInfo.processRemark" label="处理备注">{{ order.refundInfo.processRemark }}</el-descriptions-item>
        </el-descriptions>

        <!-- 操作区域 -->
        <div class="actions-area section-margin">
          <!-- 根据订单状态和配送方式显示不同的操作按钮 -->
          <el-button v-if="canAcceptOrder(order.orderStatus)" type="success" @click="handleAcceptOrder">接单</el-button>

          <!-- 跑腿员配送和商家自配送的操作按钮 -->
          <template v-if="order.deliveryType !== 2">
            <el-button v-if="canAssignDelivery(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="primary" @click="handleAssignDelivery">分配配送员</el-button>
            <el-button v-if="canStartDelivery(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="warning" @click="handleStartDelivery">开始配送</el-button>
            <el-button v-if="canCompleteDelivery(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="success" @click="handleCompleteDelivery">完成配送</el-button>
          </template>

          <!-- 到店自提的操作按钮 -->
          <template v-if="order.deliveryType === 2">
            <el-button v-if="canStartPickupPreparation(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="warning" @click="handleStartPickupPreparation">开始备餐</el-button>
            <el-button v-if="canCompletePickupPreparation(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="primary" @click="handleCompletePickupPreparation">备餐完成</el-button>
            <el-button v-if="canCompletePickup(order.orderStatus, order.deliveryInfo?.deliveryStatus)" type="success" @click="handleCompletePickup">确认自提</el-button>
          </template>

          <el-button v-if="canCancelOrder(order.orderStatus)" type="danger" @click="handleCancelOrder">取消订单</el-button>
          <!-- 退款处理按钮 -->
          <el-button v-if="order.orderStatus === OrderStatus.REFUNDING && canProcessRefund(order.orderStatus, order.refundInfo?.refundStatus)" type="success" @click="handleApproveRefund">同意退款</el-button>
          <el-button v-if="order.orderStatus === OrderStatus.REFUNDING && canProcessRefund(order.orderStatus, order.refundInfo?.refundStatus)" type="danger" @click="handleRejectRefund">拒绝退款</el-button>
          
          <!-- 调试信息区域 -->
          <div v-if="showDebugInfo" class="debug-info">
            <h4>调试信息:</h4>
            <p>订单状态: {{ order.orderStatus }} ({{ OrderStatusText[order.orderStatus as OrderStatus] }})</p>
            <p>退款信息存在: {{ !!order.refundInfo }}</p>
            <p>退款状态: {{ order.refundInfo?.refundStatus }} ({{ order.refundInfo ? getRefundStatusText(order.refundInfo.refundStatus) : '无' }})</p>
            <p>canProcessRefund结果: {{ canProcessRefund(order.orderStatus, order.refundInfo?.refundStatus) }}</p>
            <p>OrderStatus.REFUNDING值: {{ OrderStatus.REFUNDING }}</p>
            <p>状态匹配: {{ order.orderStatus === OrderStatus.REFUNDING }}</p>
            <p>退款状态匹配: {{ order.refundInfo?.refundStatus === 0 }}</p>
            
            <!-- 配送信息调试 -->
            <h5>配送信息调试:</h5>
            <p>deliveryInfo存在: {{ !!order.deliveryInfo }}</p>
            <p>deliveryStatus: {{ order.deliveryInfo?.deliveryStatus }} ({{ order.deliveryInfo ? DeliveryStatusText[order.deliveryInfo.deliveryStatus as DeliveryStatus] : '无' }})</p>
            <p>DeliveryStatus.PENDING值: {{ DeliveryStatus.PENDING }}</p>
            <p>配送状态匹配: {{ order.deliveryInfo?.deliveryStatus === DeliveryStatus.WAITING }}</p>
            <p>canAssignDelivery结果: {{ canAssignDelivery(order.orderStatus, order.deliveryInfo?.deliveryStatus) }}</p>
            <p>OrderStatus.PROCESSING值: {{ OrderStatus.PROCESSING }}</p>
            <p>订单状态是PROCESSING: {{ order.orderStatus === OrderStatus.PROCESSING }}</p>
            
            <!-- 新增退款字段调试信息 -->
            <h5>后端新增退款字段:</h5>
            <p>hasRefund: {{ order.hasRefund }}</p>
            <p>refundNo: {{ order.refundNo }}</p>
            <p>refundStatus (直接字段): {{ order.refundStatus }} ({{ order.refundStatus !== undefined ? getRefundStatusText(order.refundStatus) : '无' }})</p>
            <p>refundAmount (直接字段): {{ order.refundAmount }}</p>
            <p>refundReason (直接字段): {{ order.refundReason }}</p>
            <p>refundTime (直接字段): {{ order.refundTime }}</p>
            
            <h5>退款信息对象:</h5>
            <p>refundInfo.refundNo: {{ order.refundInfo?.refundNo }}</p>
            <p>refundInfo.refundTime: {{ order.refundInfo?.refundTime }}</p>
            <p>refundInfo.refundId: {{ order.refundInfo?.refundId }}</p>
            <p>refundInfo.refundStatus: {{ order.refundInfo?.refundStatus }} ({{ order.refundInfo ? getRefundStatusText(order.refundInfo.refundStatus) : '无' }})</p>
          </div>
          
          <!-- 调试开关 -->
          <el-button @click="toggleDebugInfo" size="small" type="info">{{ showDebugInfo ? '隐藏' : '显示' }}调试信息</el-button>
        </div>

      </div>
      <el-empty v-else-if="!loading" description="未找到订单信息或加载失败"></el-empty>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getOrderDetail,
  acceptOrder as apiAcceptOrder,
  assignDeliveryStaff as apiAssignDeliveryStaff,
  startDelivery as apiStartDelivery,
  completeDelivery as apiCompleteDelivery,
  cancelOrder as apiCancelOrder,
  processRefund as apiProcessRefund,
  processRefundByOrderId as apiProcessRefundByOrderId,
  startPickupPreparation as apiStartPickupPreparation,
  completePickupPreparation as apiCompletePickupPreparation,
  completePickup as apiCompletePickup
} from '@/modules/merchant/api/order';
import { getRefundList } from '@/modules/merchant/api/refund';
import { OrderStatus } from '@/modules/merchant/types';
import { type OrderInfo, type OrderItem, DeliveryStatus } from '@/modules/merchant/types/order';
import { OrderStatusText } from '@/modules/merchant/types';
import { DeliveryStatusText } from '@/modules/merchant/types/order';
import { formatTime } from '@/utils/format';

const route = useRoute();
const router = useRouter();

const orderId = computed(() => Number(route.params.id));
const order = ref<OrderInfo | null>(null);
const loading = ref(false);
const showDebugInfo = ref(false);

/**
 * 获取订单详情
 * @description 根据路由参数中的订单ID获取订单详细信息
 */
const fetchOrderDetail = async () => {
  if (isNaN(orderId.value)) {
    ElMessage.error('无效的订单ID');
    router.push({ name: 'MerchantOrderList' });
    return;
  }
  loading.value = true;
  try {
    const response = await getOrderDetail(orderId.value);
    // 已使用新的OrderInfo类型，与后端返回的驼峰命名一致
    const data = response as unknown as OrderInfo;
    
    // 处理后端新增的退款字段
    if (data.hasRefund || data.refundNo || data.refundStatus !== undefined || data.refundAmount !== undefined) {
      console.log('检测到后端直接返回的退款字段:', {
        hasRefund: data.hasRefund,
        refundNo: data.refundNo,
        refundStatus: data.refundStatus,
        refundAmount: data.refundAmount,
        refundReason: data.refundReason,
        refundTime: data.refundTime
      });
      
      // 如果没有refundInfo对象，则创建一个
      if (!data.refundInfo) {
        data.refundInfo = {
          refundId: 0, // 默认值，可能需要从其他地方获取
          refundAmount: data.refundAmount || 0,
          refundReason: data.refundReason || '',
          refundStatus: data.refundStatus || 0,
          applyTime: data.refundTime || new Date().toISOString(),
          refundNo: data.refundNo,
          refundTime: data.refundTime
        };
        console.log('从新字段创建退款信息对象:', data.refundInfo);
      } else {
        // 如果已有refundInfo对象，则补充新字段
        data.refundInfo.refundNo = data.refundInfo.refundNo || data.refundNo;
        data.refundInfo.refundTime = data.refundInfo.refundTime || data.refundTime;
        console.log('补充退款信息对象:', data.refundInfo);
      }
    }
    
    order.value = data;
    console.log('订单详情加载完成:', data);
    
    // 如果订单状态是REFUNDING但没有退款信息，尝试通过退款API获取
    if (data.orderStatus === OrderStatus.REFUNDING && !data.refundInfo) {
      console.log('订单状态为REFUNDING但缺少退款信息，尝试获取退款数据...');
      try {
        // 通过退款列表API查找该订单的退款信息
        const refundListResponse: any = await getRefundList({
          page: 1,
          pageSize: 10,
          // 这里可能需要根据实际API调整查询参数
        });
        
        console.log('退款列表响应:', refundListResponse);
        
        // 查找与当前订单相关的退款记录
        if (refundListResponse && refundListResponse.list) {
          const orderRefund = refundListResponse.list.find((refund: any) => 
            refund.orderId === orderId.value || 
            refund.order_id === orderId.value ||
            refund.orderNo === data.orderNo ||
            refund.order_no === data.orderNo
          );
          
          if (orderRefund) {
            console.log('找到对应的退款记录:', orderRefund);
            
            // 确保获取正确的退款ID
            let refundId = orderRefund.id || orderRefund.refundId || orderRefund.refund_id;
            if (!refundId || refundId === 0) {
              console.warn('退款记录中未找到有效的退款ID，尝试其他字段...');
              // 尝试其他可能的ID字段
              refundId = orderRefund.refundID || orderRefund.refund_ID || orderRefund.ID;
            }
            
            console.log('提取的退款ID:', refundId);
            
            // 构造退款信息
            data.refundInfo = {
              refundId: refundId || 0,
              refundAmount: orderRefund.refundAmount || orderRefund.amount || orderRefund.refund_amount || 0,
              refundReason: orderRefund.refundReason || orderRefund.reason || orderRefund.refund_reason || '用户申请退款',
              refundStatus: orderRefund.refundStatus || orderRefund.status || orderRefund.refund_status || 0,
              applyTime: orderRefund.applyTime || orderRefund.createTime || orderRefund.created_at || orderRefund.apply_time,
              processTime: orderRefund.processTime || orderRefund.processedAt || orderRefund.process_time,
              processRemark: orderRefund.processRemark || orderRefund.remark || orderRefund.process_remark
            };
            
            // 如果退款ID仍然为0，记录警告
            if (!data.refundInfo.refundId || data.refundInfo.refundId === 0) {
              console.error('警告：退款ID为0或未定义，这将导致退款操作失败！', {
                原始退款记录: orderRefund,
                构造的退款信息: data.refundInfo
              });
            }
            
            order.value = { ...data };
            console.log('已补充退款信息:', data.refundInfo);
          } else {
            console.log('未找到对应的退款记录');
          }
        }
      } catch (refundError) {
        console.error('获取退款信息失败:', refundError);
      }
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败');
    order.value = null;
  } finally {
    loading.value = false;
  }
};

/**
 * 返回订单列表页面
 */
const goBack = () => {
  router.push({ name: 'MerchantOrderList' });
};

/**
 * 计算订单商品总金额
 * @param items 订单商品列表
 * @returns 商品总金额
 */
const calculateItemsTotalAmount = (items: OrderItem[]) => {
  return items.reduce((sum, item) => sum + item.price * item.quantity, 0);
};

/**
 * 根据订单状态获取标签类型
 * @param status 订单状态
 * @returns Element Plus 标签类型
 */
const statusTagType = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.PENDING:
    case OrderStatus.CANCELLED:
    case OrderStatus.REFUNDING:
    case OrderStatus.REFUNDED:
      return 'info';
    case OrderStatus.PAID:
      return 'warning';
    case OrderStatus.PROCESSING:
    case OrderStatus.DELIVERING:
      return 'primary';
    case OrderStatus.COMPLETED:
      return 'success';
    default:
      return 'info';
  }
};

// --- 订单操作 --- 
/**
 * 判断是否可以接单
 * @param status 订单状态
 * @returns 是否可以接单
 */
const canAcceptOrder = (status: OrderStatus) => {
  return status === OrderStatus.PAID; // 只有已支付的订单可以接单
};

/**
 * 处理接单操作
 * @description 商家接受已支付的订单
 */
const handleAcceptOrder = async () => {
  if (!order.value) return;
  try {
    await ElMessageBox.confirm('确定要接受该订单吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    loading.value = true;
    await apiAcceptOrder(order.value.orderID);
    ElMessage.success('接单成功');
    fetchOrderDetail(); // 重新获取订单详情以更新状态
  } catch (error) {
    // 如果是 ElMessageBox 的取消操作，error会是 'cancel'
    if (error !== 'cancel') {
      console.error('接单失败:', error);
      ElMessage.error('接单失败');
    }
  } finally {
    loading.value = false;
  }
};


// --- 订单操作条件判断 ---
/**
 * 判断是否可以分配配送员
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以分配配送员
 */
const canAssignDelivery = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  // 类型安全检查：如果deliveryStatus为undefined则直接返回false
  if (deliveryStatus === undefined) return false;
  
  // 将deliveryStatus转换为number类型，以确保与枚举值比较的一致性
  const deliveryStatusNum = Number(deliveryStatus);
  
  // 订单状态为处理中，且配送状态为待接单或待配送时，可以分配配送员
  return status === OrderStatus.PROCESSING && (
    deliveryStatusNum === DeliveryStatus.PENDING ||  // 待接单状态可以分配配送员
    deliveryStatusNum === DeliveryStatus.WAITING     // 待配送状态可以分配配送员
  );
};

/**
 * 判断是否可以开始配送
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以开始配送
 */
const canStartDelivery = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  // 类型安全检查：如果deliveryStatus为undefined则直接返回false
  if (deliveryStatus === undefined) return false;
  
  // 将deliveryStatus转换为number类型，以确保与枚举值比较的一致性
  const deliveryStatusNum = Number(deliveryStatus);
  
  // 订单状态为处理中，且配送状态为已接单时，可以开始配送（取餐）
  return status === OrderStatus.PROCESSING && (
    deliveryStatusNum === DeliveryStatus.ACCEPTED  // 已接单状态可以开始配送
  );
};

/**
 * 判断是否可以完成配送
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以完成配送
 */
const canCompleteDelivery = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  // 类型安全检查：如果deliveryStatus为undefined则直接返回false
  if (deliveryStatus === undefined) return false;
  
  // 将deliveryStatus转换为number类型，以确保与枚举值比较的一致性
  const deliveryStatusNum = Number(deliveryStatus);
  
  // 订单状态为配送中，或配送状态为已取餐或配送中时，可以完成配送
  return status === OrderStatus.DELIVERING || (
    status === OrderStatus.PROCESSING && (
      deliveryStatusNum === DeliveryStatus.PICKED_UP ||  // 已取餐状态可以完成配送
      deliveryStatusNum === DeliveryStatus.DELIVERING    // 配送中状态可以完成配送
    )
  );
};

/**
 * 判断是否可以取消订单
 * @param status 订单状态
 * @returns 是否可以取消订单
 */
const canCancelOrder = (status: OrderStatus) => {
  return status === OrderStatus.PENDING || status === OrderStatus.PAID;
};

// --- 到店自提相关操作判断 ---
/**
 * 判断是否可以开始备餐（到店自提）
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以开始备餐
 */
const canStartPickupPreparation = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  if (deliveryStatus === undefined) return false;
  const deliveryStatusNum = Number(deliveryStatus);
  // 订单状态为处理中，且配送状态为待配送时，可以开始备餐
  return status === OrderStatus.PROCESSING && deliveryStatusNum === DeliveryStatus.WAITING;
};

/**
 * 判断是否可以完成备餐（到店自提）
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以完成备餐
 */
const canCompletePickupPreparation = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  if (deliveryStatus === undefined) return false;
  const deliveryStatusNum = Number(deliveryStatus);
  // 订单状态为处理中，且配送状态为已接单时，可以完成备餐
  // 注意：PICKING(30)状态表示已经备餐完成，等待自提，不应该再显示备餐完成按钮
  return status === OrderStatus.PROCESSING && deliveryStatusNum === DeliveryStatus.ACCEPTED;
};

/**
 * 判断是否可以确认自提（到店自提）
 * @param status 订单状态
 * @param deliveryStatus 配送状态
 * @returns 是否可以确认自提
 */
const canCompletePickup = (status: OrderStatus, deliveryStatus?: DeliveryStatus) => {
  if (deliveryStatus === undefined) return false;
  const deliveryStatusNum = Number(deliveryStatus);
  // 当配送状态为PICKING(30)时，表示备餐完成，等待自提，此时可以确认自提
  // 或者配送状态为已取餐、配送中时，也可以确认自提
  return (status === OrderStatus.PROCESSING || status === OrderStatus.DELIVERING) && (
    deliveryStatusNum === DeliveryStatus.PICKING ||      // 30-取餐中状态，等待自提
    deliveryStatusNum === DeliveryStatus.PICKED_UP ||
    deliveryStatusNum === DeliveryStatus.DELIVERING
  );
};

/**
 * 判断是否可以处理退款
 * @param status 订单状态
 * @param refundStatus 退款状态
 * @returns 是否可以处理退款
 */
const canProcessRefund = (status: OrderStatus, refundStatus?: number) => {
  // 获取订单的直接退款状态字段（如果存在）
  const directRefundStatus = order.value?.refundStatus;
  
  // 使用嵌套对象中的退款状态或直接字段中的退款状态
  const effectiveRefundStatus = refundStatus !== undefined ? refundStatus : directRefundStatus;
  
  console.log('canProcessRefund调试:', {
    status,
    refundStatus,
    directRefundStatus,
    effectiveRefundStatus,
    isRefunding: status === OrderStatus.REFUNDING,
    isPending: effectiveRefundStatus === 0,
    OrderStatusREFUNDING: OrderStatus.REFUNDING,
    result: status === OrderStatus.REFUNDING && effectiveRefundStatus === 0
  });
  
  // 退款状态：0-申请中，1-商家同意，2-退款成功，3-退款失败，4-已拒绝
  // 只有订单状态为退款中且退款状态为申请中(0)时才显示退款处理按钮
  return status === OrderStatus.REFUNDING && effectiveRefundStatus === 0;
};

/**
 * 获取退款状态文本
 * @param status 退款状态码
 * @returns 退款状态文本
 */
const getRefundStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '申请中',
    1: '商家同意',
    2: '退款成功',
    3: '退款失败',
    4: '已拒绝'
  };
  return statusMap[status] || '未知状态';
};

/**
 * 获取退款状态标签类型
 * @param status 退款状态码
 * @returns Element Plus 标签类型
 */
const getRefundStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'primary',
    2: 'success',
    3: 'danger',
    4: 'danger'
  };
  return typeMap[status] || 'info';
};

/**
 * 获取配送方式文本
 * @param deliveryType 配送方式码
 * @returns 配送方式文本
 */
const getDeliveryTypeText = (deliveryType: number) => {
  const typeMap: Record<number, string> = {
    0: '跑腿员配送',
    1: '商家自配送',
    2: '到店自提'
  };
  return typeMap[deliveryType] || '未知配送方式';
};

/**
 * 获取配送方式标签类型
 * @param deliveryType 配送方式码
 * @returns Element Plus 标签类型
 */
const getDeliveryTypeTagType = (deliveryType: number) => {
  const typeMap: Record<number, string> = {
    0: 'primary',
    1: 'success',
    2: 'info'
  };
  return typeMap[deliveryType] || 'info';
};

/**
 * 获取自提状态文本（基于配送状态转换）
 * @param deliveryStatus 配送状态
 * @returns 自提状态文本
 */
const getPickupStatusText = (deliveryStatus: DeliveryStatus) => {
  const statusMap: Record<DeliveryStatus, string> = {
    [DeliveryStatus.WAITING]: '等待接单',
    [DeliveryStatus.PENDING]: '备餐中',
    [DeliveryStatus.ACCEPTED]: '备餐中',
    [DeliveryStatus.PICKING]: '等待自提',      // 30-取餐中状态对应等待自提
    [DeliveryStatus.PICKED_UP]: '等待自提',
    [DeliveryStatus.DELIVERING]: '等待自提',
    [DeliveryStatus.COMPLETED]: '自提完成',
    [DeliveryStatus.CANCELLED]: '已取消'
  };
  return statusMap[deliveryStatus] || '未知状态';
};

/**
 * 获取自提状态标签类型
 * @param deliveryStatus 配送状态
 * @returns Element Plus 标签类型
 */
const getPickupStatusTagType = (deliveryStatus: DeliveryStatus) => {
  const typeMap: Record<DeliveryStatus, string> = {
    [DeliveryStatus.WAITING]: 'info',
    [DeliveryStatus.PENDING]: 'warning',
    [DeliveryStatus.ACCEPTED]: 'warning',
    [DeliveryStatus.PICKING]: 'primary',      // 30-取餐中状态对应等待自提，用primary标签
    [DeliveryStatus.PICKED_UP]: 'primary',
    [DeliveryStatus.DELIVERING]: 'primary',
    [DeliveryStatus.COMPLETED]: 'success',
    [DeliveryStatus.CANCELLED]: 'danger'
  };
  return typeMap[deliveryStatus] || 'info';
};

// --- 更多订单操作处理函数 ---
/**
 * 处理分配配送员操作
 * @description 为订单分配配送员
 */
const handleAssignDelivery = async () => {
  if (!order.value) return;
  try {
    const { value: deliveryStaffIdStr } = await ElMessageBox.prompt('请输入配送员ID', '分配配送员', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'number',
      inputValidator: (val: string) => !!val && !isNaN(Number(val)) && Number(val) > 0 ? true : '请输入有效的配送员ID',
    });
    const deliveryStaffId = Number(deliveryStaffIdStr);
    loading.value = true;
    await apiAssignDeliveryStaff(order.value.orderID, deliveryStaffId);
    ElMessage.success('分配配送员成功');
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('分配配送员失败:', error);
      ElMessage.error('分配配送员失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理开始配送操作
 * @description 配送员开始配送订单
 */
const handleStartDelivery = async () => {
  if (!order.value) return;
  try {
    const deliveryStaffId = order.value.deliveryInfo?.deliveryStaffId;
    if (!deliveryStaffId) {
      ElMessage.error('未找到配送员信息，无法开始配送');
      return;
    }
    const { value: estimatedArrivalTime } = await ElMessageBox.prompt('请输入预计送达时间 (例如: 12:30 或 30分钟后)', '开始配送', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'text',
      inputPlaceholder: '例如: 12:30 或 30分钟后',
      inputValidator: (val: string) => !!val ? true : '请输入预计送达时间',
    });
    loading.value = true;
    await apiStartDelivery(order.value.orderID, deliveryStaffId, estimatedArrivalTime);
    ElMessage.success('开始配送成功');
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始配送失败:', error);
      ElMessage.error('开始配送失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理完成配送操作
 * @description 标记订单配送完成
 */
const handleCompleteDelivery = async () => {
  if (!order.value) return;
  try {
    await ElMessageBox.confirm('确定该订单已完成配送吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    });
    const deliveryStaffId = order.value.deliveryInfo?.deliveryStaffId;
    if (!deliveryStaffId) {
      ElMessage.error('未找到配送员信息，无法完成配送');
      return;
    }
    loading.value = true;
    await apiCompleteDelivery(order.value.orderID, deliveryStaffId);
    ElMessage.success('订单完成成功');
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成配送失败:', error);
      ElMessage.error('完成配送失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理取消订单操作
 * @description 取消订单并记录取消原因
 */
const handleCancelOrder = async () => {
  if (!order.value) return;
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消订单的原因 (选填)', '取消订单', {
      confirmButtonText: '确定取消',
      cancelButtonText: '返回',
      inputType: 'textarea',
    });
    loading.value = true;
    await apiCancelOrder(order.value.orderID, reason || '');
    ElMessage.success('订单取消成功');
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error);
      ElMessage.error('取消订单失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理同意退款操作
 * @description 商家同意用户的退款申请
 */
const handleApproveRefund = async () => {
  console.log('handleApproveRefund 调用', order.value);
  if (!order.value?.refundInfo) {
    ElMessage.error('退款信息不存在，无法处理退款');
    return;
  }
  
  // 检查退款ID是否有效
  const refundId = order.value.refundNo;
  if (!refundId || refundId === '') {
    console.error('退款ID无效:', refundId);
    ElMessage.error('退款ID无效，无法处理退款申请。请联系技术支持。');
    return;
  }
  
  try {
    await ElMessageBox.confirm('确定同意此退款申请吗？', '确认同意退款', {
      confirmButtonText: '确定同意',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    loading.value = true;
    console.log('准备处理退款，退款ID:', refundId);
    
    try {
      // 首先尝试标准的退款处理API
      await apiProcessRefund(refundId, 'approve', '');
      ElMessage.success('退款申请已同意');
    } catch (primaryError) {
      console.warn('标准退款API失败，尝试备用方案:', primaryError);
      
      // 如果标准API失败，尝试使用订单ID的备用API
       if (order.value?.id) {
         console.log('使用订单ID进行退款处理:', order.value.id);
         await apiProcessRefundByOrderId(order.value.id, 'approve');
        ElMessage.success('退款申请已同意（通过备用方案）');
      } else {
        throw primaryError; // 如果没有订单ID，抛出原始错误
      }
    }
    
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('同意退款失败:', error);
      ElMessage.error('同意退款失败，请检查网络连接或联系技术支持');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理拒绝退款操作
 * @description 商家拒绝用户的退款申请并记录拒绝原因
 */
const handleRejectRefund = async () => {
  if (!order.value?.refundInfo) {
    ElMessage.error('退款信息不存在，无法处理退款');
    return;
  }
  
  // 检查退款ID是否有效
  const refundId = order.value.refundNo;
  if (!refundId || refundId === '') {
    console.error('退款ID无效:', refundId);
    ElMessage.error('退款ID无效，无法处理退款申请。请联系技术支持。');
    return;
  }
  
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入拒绝退款的原因', '拒绝退款', {
      confirmButtonText: '确定拒绝',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputValidator: (val: string) => !!val?.trim() ? true : '请输入拒绝原因',
    });
    
    loading.value = true;
    console.log('准备拒绝退款，退款ID:', refundId);
    
    try {
      // 首先尝试标准的退款处理API
      await apiProcessRefund(refundId, 'reject', reason);
      ElMessage.success('退款申请已拒绝');
    } catch (primaryError) {
      console.warn('标准退款API失败，尝试备用方案:', primaryError);
      
      // 如果标准API失败，尝试使用订单ID的备用API
      if (order.value?.id) {
        console.log('使用订单ID进行退款处理:', order.value.id);
        await apiProcessRefundByOrderId(order.value.id, 'reject', reason);
        ElMessage.success('退款申请已拒绝（通过备用方案）');
      } else {
        throw primaryError; // 如果没有订单ID，抛出原始错误
      }
    }
    
    fetchOrderDetail();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝退款失败:', error);
      ElMessage.error('拒绝退款失败，请检查网络连接或联系技术支持');
    }
  } finally {
    loading.value = false;
  }
};

// --- 到店自提相关操作处理函数 ---
/**
 * 处理开始备餐操作（到店自提）
 * @description 商家开始为到店自提订单备餐
 */
const handleStartPickupPreparation = async () => {
  if (!order.value) return;
  try {
    await ElMessageBox.confirm('确定开始为该订单备餐吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    loading.value = true;
    await apiStartPickupPreparation(order.value.orderID);
    ElMessage.success('已开始备餐');
    fetchOrderDetail(); // 重新获取订单详情以更新状态
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始备餐失败:', error);
      ElMessage.error('开始备餐失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理完成备餐操作（到店自提）
 * @description 商家完成备餐，等待用户自提
 */
const handleCompletePickupPreparation = async () => {
  if (!order.value) return;
  try {
    await ElMessageBox.confirm('确定备餐已完成，等待用户自提吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    });
    loading.value = true;
    await apiCompletePickupPreparation(order.value.orderID);
    ElMessage.success('备餐完成，等待用户自提');
    fetchOrderDetail(); // 重新获取订单详情以更新状态
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成备餐失败:', error);
      ElMessage.error('完成备餐失败');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 处理确认自提操作（到店自提）
 * @description 用户到店自提，商家确认并验证取餐码
 */
const handleCompletePickup = async () => {
  if (!order.value) return;
  try {
    const { value: pickupCode } = await ElMessageBox.prompt('请输入用户提供的取餐码', '确认自提', {
      confirmButtonText: '确认自提',
      cancelButtonText: '取消',
      inputType: 'text',
      inputPlaceholder: '请输入取餐码',
      inputValidator: (val: string) => {
        if (!val || val.trim() === '') {
          return '请输入取餐码';
        }
        if (!/^\d{6}$/.test(val.trim())) {
          return '取餐码应为6位数字';
        }
        return true;
      },
    });

    loading.value = true;
    await apiCompletePickup(order.value.orderID, pickupCode.trim());
    ElMessage.success('自提完成');
    fetchOrderDetail(); // 重新获取订单详情以更新状态
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认自提失败:', error);
      ElMessage.error('确认自提失败，请检查取餐码是否正确');
    }
  } finally {
    loading.value = false;
  }
};

/**
 * 切换调试信息显示
 * @description 用于调试退款按钮显示问题
 */
const toggleDebugInfo = () => {
  showDebugInfo.value = !showDebugInfo.value;
};

onMounted(() => {
  fetchOrderDetail();
});
</script>

<style scoped lang="scss">
.order-detail-page {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.section-margin {
  margin-top: 20px;
}
.section-title {
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
    color: #303133;
}
.actions-area {
  margin-top: 30px;
  text-align: center;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  text-align: left;
  font-size: 14px;
  
  h4 {
    margin: 0 0 10px 0;
    color: #409eff;
  }
  
  p {
    margin: 5px 0;
    color: #606266;
  }
}
</style>
