<!--
/**
 * 取餐号查询页面
 * <AUTHOR>
 * @date 2025-01-31
 * @version 1.0.0
 * @description 商家通过取餐号查询订单页面，查询成功后跳转到对应订单详情页面
 */
-->
<template>
  <div class="pickup-query-page">
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span>取餐码查询</span>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>

      <div class="query-content">
        <!-- 查询表单 -->
        <el-form
          ref="queryFormRef"
          :model="queryForm"
          :rules="queryRules"
          label-width="100px"
          class="query-form"
          @submit.prevent="handleQuery"
        >
          <el-form-item label="取餐码" prop="pickupNumber">
            <el-input
              v-model="queryForm.pickupNumber"
              placeholder="请输入取餐码"
              clearable
              maxlength="20"
              show-word-limit
              size="large"
              class="pickup-input"
              @keyup.enter="handleQuery"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="querying"
              @click="handleQuery"
              class="query-button"
            >
              <el-icon v-if="!querying"><Search /></el-icon>
              {{ querying ? '查询中...' : '查询订单' }}
            </el-button>
            <el-button size="large" @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 查询说明 -->
        <el-alert
          title="查询说明"
          type="info"
          :closable="false"
          show-icon
          class="query-tips"
        >
          <template #default>
            <ul class="tips-list">
              <li>请输入顾客提供的取餐码进行查询</li>
              <li>取餐码通常为4-8位数字或字母组合</li>
              <li>查询成功后将自动跳转到订单详情页面</li>
              <li>如查询不到订单，请确认取餐码是否正确</li>
            </ul>
          </template>
        </el-alert>

        <!-- 最近查询记录 -->
        <div v-if="recentQueries.length > 0" class="recent-queries">
          <h4>最近查询记录</h4>
          <div class="recent-list">
            <el-tag
              v-for="(query, index) in recentQueries"
              :key="index"
              class="recent-tag"
              @click="quickQuery(query.pickupNumber)"
            >
              {{ query.pickupNumber }}
              <span class="query-time">{{ formatTime(query.queryTime) }}</span>
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { queryOrderByPickupCode } from '@/modules/merchant/api/order';
import { formatTime } from '@/utils/format';

const router = useRouter();

// 表单引用
const queryFormRef = ref();

// 查询状态
const querying = ref(false);

// 查询表单数据
const queryForm = reactive({
  pickupNumber: ''
});

// 表单验证规则
const queryRules = {
  pickupNumber: [
    { required: true, message: '请输入取餐码', trigger: 'blur' },
    { min: 2, max: 20, message: '取餐码长度应在2-20个字符之间', trigger: 'blur' },
    {
      pattern: /^[A-Za-z0-9]+$/,
      message: '取餐码只能包含字母和数字',
      trigger: 'blur'
    }
  ]
};

// 最近查询记录
interface QueryRecord {
  pickupNumber: string;
  queryTime: string;
  orderId?: number;
}

const recentQueries = ref<QueryRecord[]>([]);

/**
 * 处理查询操作
 */
const handleQuery = async () => {
  if (!queryFormRef.value) return;

  try {
    // 表单验证
    const valid = await queryFormRef.value.validate();
    if (!valid) return;

    querying.value = true;

    // 调用查询API
    const response = await queryOrderByPickupCode(queryForm.pickupNumber);
    
    if (response && response.order_id) {
      // 查询成功，保存查询记录
      saveQueryRecord(queryForm.pickupNumber, response.order_id);
      
      // 显示成功消息
      ElMessage.success(`查询成功！订单号：${response.order_no || response.order_id}`);
      
      // 跳转到订单详情页面
      router.push({ 
        name: 'MerchantOrderDetail', 
        params: { id: response.order_id.toString() } 
      });
    } else {
      ElMessage.error('未找到对应的订单，请检查取餐码是否正确');
    }
  } catch (error: any) {
    console.error('查询订单失败:', error);
    
    // 根据错误类型显示不同的错误消息
    if (error?.response?.status === 404) {
      ElMessage.error('未找到对应的订单，请检查取餐码是否正确');
    } else if (error?.response?.status === 400) {
      ElMessage.error('取餐码格式不正确，请重新输入');
    } else {
      ElMessage.error(error?.message || '查询失败，请稍后重试');
    }
  } finally {
    querying.value = false;
  }
};

/**
 * 重置表单
 */
const resetForm = () => {
  queryFormRef.value?.resetFields();
  queryForm.pickupNumber = '';
};

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1);
};

/**
 * 快速查询（点击最近记录）
 */
const quickQuery = (pickupNumber: string) => {
  queryForm.pickupNumber = pickupNumber;
  handleQuery();
};

/**
 * 保存查询记录
 */
const saveQueryRecord = (pickupNumber: string, orderId: number) => {
  const record: QueryRecord = {
    pickupNumber,
    queryTime: new Date().toISOString(),
    orderId
  };

  // 添加到最近查询记录
  recentQueries.value.unshift(record);
  
  // 只保留最近10条记录
  if (recentQueries.value.length > 10) {
    recentQueries.value = recentQueries.value.slice(0, 10);
  }

  // 保存到本地存储
  localStorage.setItem('pickup_query_records', JSON.stringify(recentQueries.value));
};

/**
 * 加载最近查询记录
 */
const loadRecentQueries = () => {
  try {
    const stored = localStorage.getItem('pickup_query_records');
    if (stored) {
      recentQueries.value = JSON.parse(stored);
    }
  } catch (error) {
    console.error('加载查询记录失败:', error);
    recentQueries.value = [];
  }
};

// 组件挂载时加载最近查询记录
onMounted(() => {
  loadRecentQueries();
});
</script>

<style scoped>
.pickup-query-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.query-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.query-content {
  padding: 20px 0;
}

.query-form {
  max-width: 500px;
  margin: 0 auto 30px;
}

.pickup-input {
  font-size: 16px;
}

.query-button {
  width: 150px;
  margin-right: 20px;
}

.query-tips {
  margin: 30px 0;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
}

.tips-list li {
  margin: 8px 0;
  color: #606266;
}

.recent-queries {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.recent-queries h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.recent-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.recent-tag {
  cursor: pointer;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
}

.recent-tag:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.query-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

@media (max-width: 768px) {
  .pickup-query-page {
    padding: 10px;
  }
  
  .query-form {
    max-width: 100%;
  }
  
  .query-button {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
