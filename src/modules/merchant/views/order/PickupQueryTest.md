# 取餐码查询功能测试文档

## 功能概述
通过取餐码查询订单的页面，查询成功后跳转到对应订单详情页面。

## 测试用例

### 1. 页面访问测试
- **测试路径**: `/merchant/order/pickup-query`
- **预期结果**: 页面正常加载，显示取餐码查询表单

### 2. 表单验证测试

#### 2.1 空值验证
- **操作**: 不输入任何内容，点击查询按钮
- **预期结果**: 显示"请输入取餐码"的错误提示

#### 2.2 长度验证
- **操作**: 输入1个字符，点击查询按钮
- **预期结果**: 显示"取餐码长度应在2-20个字符之间"的错误提示

#### 2.3 格式验证
- **操作**: 输入包含特殊字符的取餐码（如"ABC-123"）
- **预期结果**: 显示"取餐码只能包含字母和数字"的错误提示

#### 2.4 正确格式验证
- **操作**: 输入正确格式的取餐码（如"ABC123"）
- **预期结果**: 表单验证通过，可以提交查询

### 3. API调用测试

#### 3.1 查询成功
- **测试数据**: 存在的取餐码
- **预期结果**:
  - 显示查询成功消息
  - 自动跳转到订单详情页面
  - 保存查询记录到本地存储

#### 3.2 查询失败 - 订单不存在
- **测试数据**: 不存在的取餐码
- **预期结果**: 显示"未找到对应的订单，请检查取餐码是否正确"的错误消息

#### 3.3 查询失败 - 网络错误
- **测试条件**: 断网或服务器错误
- **预期结果**: 显示"查询失败，请稍后重试"的错误消息

### 4. 用户体验测试

#### 4.1 加载状态
- **操作**: 点击查询按钮
- **预期结果**: 按钮显示"查询中..."状态，禁用按钮防止重复提交

#### 4.2 回车键支持
- **操作**: 在输入框中按回车键
- **预期结果**: 自动触发查询操作

#### 4.3 最近查询记录
- **操作**: 成功查询后刷新页面
- **预期结果**: 显示最近查询记录，点击可快速查询

#### 4.4 响应式设计
- **测试条件**: 移动端设备或缩小浏览器窗口
- **预期结果**: 页面布局适配移动端，按钮和表单元素正确显示

### 5. 导航测试

#### 5.1 返回按钮
- **操作**: 点击页面右上角的"返回"按钮
- **预期结果**: 返回到上一页

#### 5.2 菜单导航
- **操作**: 通过左侧菜单"外卖订单管理" -> "取餐码查询"访问页面
- **预期结果**: 正确跳转到取餐码查询页面

## API接口信息

### 查询接口
- **URL**: `/v1/merchant/takeout/orders/pickup/query`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "pickupCode": "ABC123"
  }
  ```
- **成功响应**:
  ```json
  {
    "order_id": 12345,
    "order_no": "ORD20250131001"
  }
  ```
- **失败响应**:
  ```json
  {
    "code": 404,
    "message": "订单不存在"
  }
  ```

## 注意事项

1. 确保商家已登录且通过审核
2. 取餐码查询功能需要商家权限
3. 查询记录保存在本地存储中，清除浏览器数据会丢失
4. 页面支持键盘操作，提升用户体验
5. 错误处理覆盖各种异常情况

## 已知问题

目前没有已知问题。

## 更新日志

- 2025-01-31: 初始版本创建
  - 实现基本查询功能
  - 添加表单验证
  - 实现错误处理
  - 添加最近查询记录功能
  - 实现响应式设计
