<template>
  <div class="merchant-layout">
    <!-- 头部区域 -->
    <header class="merchant-header">
      <div class="logo-container">
        <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
        <h1 class="title">商家中心</h1>
      </div>
      
      <div class="header-right">
        <!-- 通知图标 -->
        <div v-if="merchantStore.isApproved" class="notification-icon" @click="toggleChat">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon :size="20">
              <ChatDotRound />
            </el-icon>
          </el-badge>
        </div>
        
        <!-- 聊天组件 -->
        <UnifiedChatWindow
          v-if="merchantStore.isApproved"
          :visible="showChat"
          :minimized="chatMinimized"
          title="客服聊天"
          @close="closeChat"
          @minimize="minimizeChat"
          @restore="restoreChat"
          @unread-change="handleUnreadChange"
          @position-change="handlePositionChange"
          @size-change="handleSizeChange"
        />
        
        <!-- 用户菜单 -->
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="user-info">
            <el-avatar :src="merchantStore.logo" :size="32" />
            <span class="merchant-name">{{ merchantStore.merchantName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">账户设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <div class="merchant-container">
      <!-- 侧边栏导航 -->
      <aside class="merchant-sidebar" :class="{ 'collapse': isCollapse }" v-if="merchantStore.isLoggedIn">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :router="true"
          :collapse="isCollapse"
        >
          <!-- 静态菜单项 -->
          <el-menu-item index="/merchant/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <span>控制台</span>
          </el-menu-item>
          
          <!-- 动态菜单项循环显示 -->
          <template v-for="item in menuItems" :key="item.key">
            <!-- 跳过静态菜单项 -->
            <template v-if="item.dynamic || (item.key !== 'dashboard' && item.key !== 'settings')">
              <!-- 有子菜单的情况 -->
              <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
                <template #title>
                  <el-icon>
                    <component :is="item.icon || Menu" />
                  </el-icon>
                  <span>{{ item.label }}</span>
                </template>
                <el-menu-item 
                  v-for="child in item.children" 
                  :key="child.key" 
                  :index="child.path"
                >
                  <span>{{ child.label }}</span>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 没有子菜单的情况 -->
              <el-menu-item v-else :index="item.path">
                <el-icon>
                  <component :is="item.icon || Menu" />
                </el-icon>
                <template #title>{{ item.label }}</template>
              </el-menu-item>
            </template>
          </template>
          
          <!-- 账户设置菜单项 -->
          <el-menu-item index="/merchant/settings">
            <el-icon><Setting /></el-icon>
            <span>账户设置</span>
          </el-menu-item>
        </el-menu>
        
        <!-- 收起/展开按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon :size="20">
            <component :is="isCollapse ? Expand : Fold" />
          </el-icon>
        </div>
      </aside>
      
      <!-- 主内容区域 -->
      <main class="merchant-main">
        <!-- 面包屑导航 -->
        <el-breadcrumb v-if="breadcrumbs.length > 0" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/merchant/dashboard' }">控制台</el-breadcrumb-item>
          <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index" :to="item.path">
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        
        <!-- 路由视图 -->
        <div class="content-container">
          <!-- 添加全局加载状态 -->
          <div v-if="isRouteLoading" class="global-loading">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <p>加载中...</p>
          </div>

          <router-view v-slot="{ Component }">
            <div>
              <!-- 直接使用组件，避免多层嵌套导致的DOM问题 -->
              <component v-if="Component" :is="Component" />
              
              <!-- 仅在没有组件时显示加载状态 -->
              <div v-else class="page-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <p>页面加载中...</p>
              </div>
            </div>
          </router-view>
        </div>
      </main>
    </div>
    
    <!-- 聊天功能已集成到MerchantChat组件中 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { 
  HomeFilled, 
  Setting, 
  ArrowDown,
  Menu,
  Fold,
  Expand,
  Loading,
  ChatDotRound
} from '@element-plus/icons-vue';
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore';
import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';
import UnifiedChatWindow from '@/components/UnifiedChatWindow.vue';
import { /* useSessionStore, */ useChatStore } from '@/modules/chat/stores';
import { getUnreadCount } from '@/modules/chat/api';
import { uploadConfigService } from '@/modules/chat/services/uploadConfig';
import { setupMessageHandlers, cleanupMessageHandlers } from '@/modules/chat/handlers';
const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

// 移除商家专用WebSocket服务，统一使用chatStore
// const chatWebSocketService = ref(getChatWebSocketService());
// provide('chatWebSocketService', chatWebSocketService);


// 定义菜单项和路径数据的接口
interface MenuItem {
  key: string;
  label: string;
  icon?: string;
  path: string;
  children?: MenuItem[];
  dynamic?: boolean;
}

interface PathItem {
  path: string;
  title: string;
  icon?: string;
  count?: number;
  [key: string]: any;
}

interface ModuleData {
  module: string;
  paths: PathItem[];
}

// 路由与商家store
const route = useRoute();
const router = useRouter();
const merchantStore = useMerchantStore();
const chatStore = useChatStore();
// const sessionStore = useSessionStore(); // 未使用，注释掉

// 提供统一的chatStore给子组件
provide('chatStore', chatStore);
// const systemStore = useSystemStore();
// const systemInfo = computed(() => systemStore.systemInfo);

// 侧边栏状态
const isCollapse = ref(false);
const screenWidth = ref(window.innerWidth);

// 路由加载状态
const isRouteLoading = ref(false);

// 聊天组件显示状态
const showChat = ref(false);
const chatMinimized = ref(false);

// 🔧 通知点击事件处理器
const handleOpenChatUI = (event: CustomEvent) => {
  const { sessionId } = event.detail
  console.log('🔔 [MerchantLayout] 收到打开聊天UI事件:', sessionId)

  // 打开聊天窗口
  if (!showChat.value) {
    toggleChat()
  }

  // 切换到指定会话
  if (sessionId) {
    chatStore.currentSessionId = sessionId
  }
}

// 计算未读消息总数
const unreadCount = computed(() => {
  // 优先使用chatStore中的totalUnreadCount
  return chatStore.totalUnreadCount || 0;
});

// 响应式布局断点
const MOBILE_BREAKPOINT = 992; // 在992px以下自动折叠侧边栏

// 移除通知相关状态，改为使用聊天组件

// 静态菜单项 - 只保留dashboard
const staticMenuItems = ref<MenuItem[]>([
  {
    key: 'dashboard',
    label: '控制台',
    icon: 'HomeFilled',
    path: '/merchant/dashboard'
  },
  {
    key: 'promotion',
    label: '促销管理',
    icon: 'Discount',
    path: '/merchant/promotion',
    children: [
      {
        key: 'promotion-list',
        label: '促销活动',
        path: '/merchant/promotion/list',
        dynamic: true
      },
      {
        key: 'coupon-list',
        label: '优惠券管理',
        path: '/merchant/coupon/list',
        dynamic: true
      }
    ]
  },
  {
    key: 'takeout-order',
    label: '外卖订单管理',
    icon: 'List', // Main icon for the group
    path: '/merchant/order/list', // Added path for the group
    children: [
      { key: 'takeout-order-list', label: '订单列表', path: '/merchant/order/list', icon: 'List' },
      { key: 'takeout-order-pickup-query', label: '取餐号查询', path: '/merchant/order/pickup-query', icon: 'Search' },
      { key: 'takeout-order-statistics', label: '订单统计', path: '/merchant/order/statistics', icon: 'DataAnalysis' },
    ]
  },
  {
    key: 'settings',
    label: '账户设置',
    icon: 'Setting',
    path: '/merchant/settings'
  }]);

// 所有菜单项 - 包含静态和动态生成的
const menuItems = ref<MenuItem[]>([...staticMenuItems.value]);

// 处理前端路径数据，生成动态菜单项
const generateDynamicMenuItems = (frontendPaths: ModuleData[]) => {
  // 记录菜单生成过程
  console.log('开始生成动态菜单，前端路径数据:', frontendPaths);
  
  // 创建核心菜单项数组 - 包含静态菜单项和必要的功能模块，无论前端路径是否为空
  const coreMenuItems = [...staticMenuItems.value];
  
  // 定义必要的菜单项 - 这些菜单会始终显示
  const essentialMenuItems: MenuItem[] = [
    // 外卖商品管理
    {
      key: 'takeout',
      label: '外卖管理',
      icon: 'Food',
      path: '/merchant/takeout',
      children: [
        {
          key: 'takeout-food-list',
          label: '商品管理',
          path: '/merchant/takeout/food/list',
          dynamic: true
        },
        {
          key: 'takeout-food-add',
          label: '新增商品',
          path: '/merchant/takeout/food/add',
          dynamic: true
        },
        {
          key: 'takeout-category-list',
          label: '分类管理',
          path: '/merchant/takeout/category/list',
          dynamic: true
        }
      ],
      dynamic: true
    },
    // 营业时间管理
    {
      key: 'business-hours',
      label: '营业时间管理',
      icon: 'Timer',
      path: '/merchant/business-hours',
      dynamic: true
    }
  ];
  
  // 首先添加必要菜单项到核心菜单
  essentialMenuItems.forEach(item => {
    if (!coreMenuItems.some(existing => existing.path === item.path)) {
      coreMenuItems.push(item);
    }
  });
  
  // 如果前端路径数据为空，返回核心菜单
  if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
    console.log('前端路径数据为空，使用核心商家菜单');
    return coreMenuItems;
  }

  // 使用已经包含必要菜单的核心菜单作为基础
const newMenuItems: MenuItem[] = [...coreMenuItems];
  
  // 处理前端路径数据
  frontendPaths.forEach(moduleData => {
    if (!moduleData.module || !moduleData.paths || !Array.isArray(moduleData.paths)) {
      return;
    }
    
    // 根据模块生成子菜单
    const moduleName = moduleData.module;
    const modulePaths = moduleData.paths;
    
    // 跳过空路径
    if (modulePaths.length === 0) return;
    
    // 根据模块名称创建不同的菜单结构
    switch(moduleName) {
      case 'merchant':
        // merchant模块的路径直接添加到顶级菜单
        console.log('处理merchant模块，路径数量:', modulePaths.length);
        console.log('当前菜单项:', JSON.stringify(newMenuItems.map(item => ({key: item.key, path: item.path}))));
        
        modulePaths.forEach((pathItem: PathItem) => {
          console.log('处理商家模块路径:', pathItem);
          
          // 检查是否已存在相同路径的菜单项（避免重复）
          const existingItem = newMenuItems.find(item => item.path === `/merchant/${pathItem.path}`);
          
          console.log(`路径 /merchant/${pathItem.path} 是否已存在:`, !!existingItem);
          
          if (existingItem) {
            console.log('找到冲突的菜单项:', existingItem);
          }
          
          if (!existingItem && pathItem.path && pathItem.title) {
            console.log('添加新的商家菜单项:', pathItem.title);
            newMenuItems.push({
              key: pathItem.path,
              label: pathItem.title,
              icon: pathItem.icon || 'Menu', // 使用路径中的图标或默认图标
              path: `/merchant/${pathItem.path}`,
              dynamic: true // 标记为动态生成的
            });
          } else {
            console.log('跳过添加菜单项的原因:', 
              !pathItem.path ? '路径为空' : 
              !pathItem.title ? '标题为空' : 
              '菜单项已存在');
          }
        });
        break;
        
      default:
        // 其他模块创建新的顶级菜单
        const moduleMenu: MenuItem = {
          key: moduleName,
          label: moduleName.charAt(0).toUpperCase() + moduleName.slice(1), // 首字母大写
          icon: 'Menu',
          path: `/merchant/${moduleName}`,
          children: modulePaths.map((pathItem: PathItem) => ({
            key: pathItem.path,
            label: pathItem.title,
            icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
            path: `/merchant/${moduleName}/${pathItem.path}`,
            dynamic: true
          }))
        };
        
        // 检查是否已存在相同模块名的菜单
        const existingModule = newMenuItems.find(item => item.key === moduleName);
        if (!existingModule) {
          newMenuItems.push(moduleMenu);
        }
        break;
    }
  });
  
  console.log('生成的动态菜单：', newMenuItems);
  return newMenuItems;
};

// 监听前端路径数据变化，更新菜单
watch(() => merchantStore.frontendPaths, (newPaths) => {
  console.log('监听到frontendPaths变化:', newPaths);
  // 无论前端路径数据是否为空，都应用菜单生成逻辑
  // 当为空时会使用默认菜单
  menuItems.value = generateDynamicMenuItems(newPaths);
}, { deep: true });

// 激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched;
  return matched.slice(1).map(item => {
    return {
      title: item.meta.title as string || '',
      path: item.path
    };
  });
});

// 切换侧边栏状态
function toggleSidebar() {
  isCollapse.value = !isCollapse.value;
}

// 切换聊天组件显示状态
async function toggleChat() {
  try {
    // 安全检查：确保商家已通过审核
    if (!merchantStore.isApproved) {
      console.warn('⚠️ 商家未通过审核，无法打开聊天');
      return;
    }

    const wasVisible = showChat.value;
    showChat.value = !showChat.value;

    // 同步聊天UI状态到chatStore
    chatStore.setChatUIVisible(showChat.value);

    console.log('🔄 MerchantLayout: 切换聊天组件状态', {
      wasVisible,
      newVisible: showChat.value,
      isConnected: chatStore.isConnected,
      status: chatStore.clientStatus
    });

    if (showChat.value) {
      // 连接聊天服务（如果未连接）
      if (!chatStore.isConnected) {
        console.log('🔗 MerchantLayout: WebSocket未连接，尝试连接');
        await chatStore.initializeChat({
          userType: 'merchant',
          userId: merchantStore.merchantInfo?.id
        });
      } else {
        console.log('✅ MerchantLayout: WebSocket已连接');
      }
    }
  } catch (error) {
    console.error('❌ 切换聊天组件失败:', error);
    // 发生错误时重置状态
    showChat.value = false;
    chatStore.setChatUIVisible(false);
  }
}

// 关闭聊天组件
function closeChat() {
  showChat.value = false;
  chatMinimized.value = false;
  // 同步聊天UI状态到chatStore
  chatStore.setChatUIVisible(false);
}

// 最小化聊天组件
function minimizeChat() {
  chatMinimized.value = true;
}

// 恢复聊天组件
function restoreChat() {
  chatMinimized.value = false;
}

// 处理未读消息数量变化
function handleUnreadChange(count: number) {
  console.log('商家聊天窗口未读消息数量变化:', count);
}

// 处理聊天窗口位置变化
function handlePositionChange(x: number, y: number) {
  console.log('商家聊天窗口位置变化:', { x, y });
  localStorage.setItem('merchantChatWindowPosition', JSON.stringify({ x, y }));
}

// 处理聊天窗口大小变化
function handleSizeChange(width: number, height: number) {
  console.log('商家聊天窗口大小变化:', { width, height });
  localStorage.setItem('merchantChatWindowSize', JSON.stringify({ width, height }));
}

// 窗口尺寸改变时更新状态
function handleResize() {
  screenWidth.value = window.innerWidth;
  isCollapse.value = screenWidth.value < MOBILE_BREAKPOINT;
}





// 下拉菜单命令处理
function handleCommand(command: string) {
  if (command === 'logout') {
    merchantStore.logout();
    router.push('/merchant/login');
  } else {
    router.push(`/merchant/${command}`);
  }
}

// 监听商家登录状态
watch(() => merchantStore.isLoggedIn, (isLoggedIn) => {
  if (!isLoggedIn) {
    if (route.meta.requiresAuth) {
      router.push('/merchant/login');
    }
  }
});

// 监听商家审核状态变化
watch(() => merchantStore.isApproved, async (isApproved, wasApproved) => {
  console.log('🔄 [MerchantLayout] 商家审核状态变化:', { isApproved, wasApproved, isLoggedIn: merchantStore.isLoggedIn });

  if (merchantStore.isLoggedIn) {
    if (!isApproved && route.meta.requireApproved) {
      // 商家未通过审核，跳转到状态页
      console.log('❌ [MerchantLayout] 商家未通过审核，断开聊天连接');
      router.push('/merchant/status');
      // 断开聊天连接
      if (chatStore.isConnected) {
        await chatStore.disconnectChat();
      }
    } else if (isApproved && !wasApproved && !chatStore.isConnected && !chatStore.isInitializing) {
      // 商家从未审核变为已审核，且聊天服务未连接，初始化聊天服务
      console.log('✅ [MerchantLayout] 商家通过审核，初始化聊天服务');
      await initializeChatService();
    }
  }
});



// 添加窗口尺寸变化监听
onMounted(() => {
  // 初始化时检查窗口尺寸
  handleResize();
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);

  // 🔧 清理通知点击事件监听器
  window.removeEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [MerchantLayout] 已清理通知点击事件监听器')

  // 清理消息处理器
  try {
    cleanupMessageHandlers();
    console.log('🧹 商家消息处理器已清理');
  } catch (error) {
    console.error('❌ 清理消息处理器失败:', error);
  }

  // 注意：不要在这里断开WebSocket连接！
  // WebSocket连接应该作为后台服务持续运行，用于接收订单消息、系统通知等
  // 只有在商家登出或审核状态变化时才应该断开连接
  console.log('🧹 MerchantLayout卸载，保持WebSocket连接运行');
});

// 通用的未读消息API调用函数，支持重试
const callUnreadCountAPI = async (retries = 3, delay = 1000) => {
  console.log('🔧 [callUnreadCountAPI] 函数开始执行，参数:', { retries, delay });
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`📊 第${i + 1}次尝试调用getUnreadCount API...`);
      console.log('🔧 [callUnreadCountAPI] 准备调用getUnreadCount API');
      const startTime = Date.now();
      const unreadData = await getUnreadCount();
      const endTime = Date.now();
      
      console.log('📊 getUnreadCount API调用成功:', {
        duration: `${endTime - startTime}ms`,
        response: unreadData,
        responseType: typeof unreadData,
        attempt: i + 1
      });
      
      const totalUnread = typeof unreadData === 'number' ? unreadData : (unreadData?.total || 0);
      console.log('🔧 [callUnreadCountAPI] 计算得到的totalUnread:', totalUnread);
      
      if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
        chatStore.setTotalUnreadCount(totalUnread);
        console.log('✅ 商家未读消息总数已更新:', totalUnread);
      } else {
        console.warn('⚠️ chatStore.setTotalUnreadCount方法不存在');
      }
      
      console.log('🔧 [callUnreadCountAPI] 函数执行成功，返回值:', totalUnread);
      return totalUnread;
    } catch (error: any) {
      console.error(`❌ 第${i + 1}次API调用失败:`, {
        error: error?.message || error,
        stack: error?.stack
      });
      
      if (i < retries - 1) {
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('❌ 所有重试都失败，设置默认未读数量为0');
  console.log('🔧 [callUnreadCountAPI] 所有重试失败，准备返回默认值0');
  if (chatStore && typeof chatStore.setTotalUnreadCount === 'function') {
    chatStore.setTotalUnreadCount(0);
  }
  return 0;
};

// 初始化聊天服务
const initializeChatService = async () => {
  try {
    console.log('🚀 开始检查聊天服务初始化条件:', {
      isLoggedIn: merchantStore.isLoggedIn,
      hasToken: !!merchantStore.token,
      merchantInfo: merchantStore.merchantInfo,
      auditStatus: merchantStore.merchantInfo?.audit_status,
      isApproved: merchantStore.isApproved
    });
    
    // 修改条件：只要商家已登录且有token就尝试初始化
    if (merchantStore.isLoggedIn && merchantStore.token) {
      console.log('✅ 基本条件满足，开始初始化聊天服务');
      
      // 初始化聊天store
      console.log('🔄 开始初始化聊天store，参数:', {
        userType: 'merchant',
        userId: merchantStore.merchantInfo?.id || undefined
      });
      
      const initStartTime = Date.now();
      let initSuccess = false;

      // 初始化消息处理器
      try {
        console.log('🔧 初始化商家消息处理器...');
        const messageRouter = await setupMessageHandlers('merchant', {
          debug: import.meta.env.DEV,
          timeout: 10000
        });

        // 将消息路由器集成到聊天store中
        if (typeof chatStore.setMessageRouter === 'function') {
          chatStore.setMessageRouter(messageRouter);
        } else {
          console.warn('⚠️ chatStore.setMessageRouter方法不存在，消息路由器将在后续版本中集成');
        }

        console.log('✅ 商家消息处理器初始化完成');
      } catch (handlerError) {
        console.error('❌ 消息处理器初始化失败:', handlerError);
      }

      try {
        console.log('🔄 开始初始化聊天store，参数:', {
          userType: 'merchant',
          userId: merchantStore.merchantInfo?.id || undefined
        });

        console.log('🔧 [initializeChatService] 即将调用chatStore.initializeChat');
        await chatStore.initializeChat({
          userType: 'merchant',
          userId: merchantStore.merchantInfo?.id || undefined
        });
        console.log('🔧 [initializeChatService] chatStore.initializeChat调用完成');
        
        // 直接调用未读消息API，不依赖事件
        console.log('🔧 [initializeChatService] 直接调用未读消息API');
        setTimeout(async () => {
          try {
            console.log('🔧 [initializeChatService] 开始调用callUnreadCountAPI');
            await callUnreadCountAPI();
            console.log('🔧 [initializeChatService] callUnreadCountAPI 调用成功');
          } catch (error) {
            console.error('🔧 [initializeChatService] callUnreadCountAPI 调用失败:', error);
          }
        }, 500); // 延迟500ms确保聊天服务完全初始化
        
        const initEndTime = Date.now();
        initSuccess = true;
        
        console.log('✅ 聊天store初始化完成，耗时:', `${initEndTime - initStartTime}ms`);
        console.log('📊 初始化后的聊天状态:', {
          isConnected: chatStore.isConnected,
          clientStatus: chatStore.clientStatus,
          currentUser: chatStore.currentUser,
          totalUnreadCount: chatStore.totalUnreadCount,
          error: chatStore.error,
          isLoading: chatStore.isLoading
        });
      } catch (initError: any) {
        const initEndTime = Date.now();
        console.error('❌ 聊天store初始化失败:', {
          error: initError?.message || initError,
          stack: initError?.stack,
          duration: `${initEndTime - initStartTime}ms`
        });
        // 即使初始化失败，也继续尝试获取未读消息
      }
      
      console.log('🎯 initializeChat执行完成，API调用已通过setTimeout异步处理，初始化成功:', initSuccess);
      
      console.log('✅ 商家聊天服务初始化完成');
    } else {
      console.warn('⚠️ 聊天服务初始化条件不满足:', {
        isLoggedIn: merchantStore.isLoggedIn,
        hasToken: !!merchantStore.token,
        reason: !merchantStore.isLoggedIn ? '商家未登录' : '缺少认证token'
      });
      
      // 如果商家信息不存在但已登录，尝试获取商家信息
      if (merchantStore.isLoggedIn && !merchantStore.merchantInfo) {
        console.log('🔄 商家已登录但信息不存在，尝试获取商家信息...');
        try {
          const fetchStartTime = Date.now();
          await merchantStore.fetchMerchantInfo();
          const fetchEndTime = Date.now();
          console.log('✅ 获取商家信息完成，耗时:', `${fetchEndTime - fetchStartTime}ms`);
          
          // 重新尝试初始化
          if (merchantStore.token) {
            console.log('🔄 获取商家信息后重新初始化聊天服务');
            await initializeChatService();
            return;
          }
        } catch (error: any) {
          console.error('❌ 获取商家信息失败:', {
            error: error?.message || error,
            stack: error?.stack
          });
        }
      }
    }
  } catch (error: any) {
    console.error('❌ 初始化聊天服务失败:', {
      error: error?.message || error,
      stack: error?.stack
    });
  }
};

// 组件挂载时加载商家信息
onMounted(async () => {
  console.log('=== MerchantLayout 组件开始挂载 ===');
  console.log('🔍 初始状态检查:', {
    isLoggedIn: merchantStore.isLoggedIn,
    token: !!merchantStore.token,
    merchantInfo: merchantStore.merchantInfo,
    auditStatus: merchantStore.merchantInfo?.audit_status,
    isApproved: merchantStore.isApproved
  });

  // 设置聊天上传配置为商家类型
  uploadConfigService.setUserType('merchant');
  console.log('🔧 MerchantLayout: 聊天上传配置设置为商家类型');

  // 🔧 添加通知点击事件监听器
  window.addEventListener('openChatUI', handleOpenChatUI as EventListener)
  console.log('🔧 [MerchantLayout] 已添加通知点击事件监听器')

  // 尝试恢复登录状态
  try {
    // 先尝试使用 retoken 恢复当前 token 状态
    console.log('🔄 开始尝试恢复token状态...');
    const tokenRestored = await merchantStore.retoken();
    console.log('✅ token恢复状态:', tokenRestored, '当前登录状态:', merchantStore.isLoggedIn);
    
    // 如果有 token 但没有商家信息，获取商家信息
    if (merchantStore.isLoggedIn && !merchantStore.merchantInfo) {
      console.log('🔄 已有token但缺少商家信息，开始获取商家信息...');
      const merchantInfo = await merchantStore.fetchMerchantInfo();
      console.log('✅ 商家信息获取完成:', {
        merchantInfo,
        auditStatus: merchantInfo?.audit_status,
        isApproved: merchantStore.isApproved,
        expectedApprovedValue: 1
      });
    }
    // 如果没有登录但需要认证，尝试使用长期token登录
    else if (!merchantStore.isLoggedIn && route.meta.requiresAuth) {
      console.log('🔄 未登录但需要认证，尝试使用长期token登录...');
      const success = await merchantStore.loginByLongTermTokenAction();
      console.log('🔍 长期token登录结果:', success);
      if (!success && route.meta.requiresAuth) {
        console.log('❌ 长期token登录失败，跳转到登录页');
        router.push('/merchant/login');
        return;
      }
    }
    
    // 检查已登录状态下是否需要跳转到审核页
    if (merchantStore.isLoggedIn && merchantStore.merchantInfo) {
      console.log('🔍 检查商家审核状态:', {
        auditStatus: merchantStore.merchantInfo.audit_status,
        isApproved: merchantStore.isApproved,
        requireApproved: route.meta.requireApproved
      });
      
      // 如果需要审核但未通过，跳转到状态页
      if (route.meta.requireApproved && !merchantStore.isApproved) {
        console.log('❌ 商家需要审核但未通过，跳转到状态页');
        router.push('/merchant/status');
      }
      
      // 加载统计数据
      if (merchantStore.isApproved) {
        console.log('✅ 商家已通过审核，开始加载统计数据...');
        await merchantStore.fetchStatistics();
        console.log('✅ 统计数据加载完成');
      }
      
      // 初始化聊天服务（只有在已登录、已审核且未连接时才初始化）
      if (merchantStore.isLoggedIn && merchantStore.token && merchantStore.isApproved &&
          !chatStore.isConnected && !chatStore.isInitializing) {
        console.log('🚀 [MerchantLayout] 组件挂载时商家已登录且已审核，初始化聊天服务');
        await initializeChatService();
        console.log('✅ 聊天服务初始化完成');
      } else {
        console.log('ℹ️ [MerchantLayout] 跳过聊天服务初始化:', {
          isLoggedIn: merchantStore.isLoggedIn,
          hasToken: !!merchantStore.token,
          isApproved: merchantStore.isApproved,
          isConnected: chatStore.isConnected,
          isInitializing: chatStore.isInitializing
        });
      }
      

    } else if (route.meta.requiresAuth) {
      // 如果需要登录但没有登录，跳转到登录页
      console.log('❌ 需要登录但未登录，跳转到登录页');
      router.push('/merchant/login');
    }
  } catch (error) {
    console.error('❌ 初始化商家信息出错:', error);
    // Token失效或出错，跳转登录页
    if (route.meta.requiresAuth) {
      router.push('/merchant/login');
    }
  }
  
  console.log('商家布局组件挂载完成，当前状态:', {
    isLoggedIn: merchantStore.isLoggedIn,
    hasInfo: !!merchantStore.merchantInfo,
    isApproved: merchantStore.isApproved
  });
  
  // 获取前端路径数据，用于生成动态菜单
  try {
    console.log('获取商家模块前端路径数据');
    
    // 不要清除缓存，而是使用缓存的数据（如果有的话）
    // 这样即使API请求失败，也能保留之前的菜单
    // await merchantStore.clearFrontendPathsCache(); // 注释掉清除缓存的操作
    
    // 获取前端路径数据
    const paths = await merchantStore.fetchFrontendPaths(false); // 先尝试从缓存获取
    console.log('获取到前端路径数据:', paths);
    
    // 确保更新菜单数据（不管是否为空）
    menuItems.value = generateDynamicMenuItems(paths);
    
    if (!paths || paths.length === 0) {
      console.warn('获取的前端路径数据为空，尝试重新获取...');
      // 延迟再试一次，这次强制从服务器获取
      setTimeout(async () => {
        const retryPaths = await merchantStore.fetchFrontendPaths(true); // 强制刷新
        console.log('重试获取前端路径数据:', retryPaths);
        // 重要：确保重试后也更新菜单
        menuItems.value = generateDynamicMenuItems(retryPaths);
      }, 1000);
    }
  } catch (error) {
    console.error('获取前端路径数据失败:', error);
    // 即使出错，也应用默认菜单
    menuItems.value = generateDynamicMenuItems([]);
  }
});
</script>

<style lang="scss" scoped>
//@use '@/styles/variables.scss' as *;
html, body {
  overflow: hidden; /* 禁止滚动条 */
}
.merchant-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .merchant-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
    
    .logo-container {
      display: flex;
      align-items: center;
      
      .logo {
        height: 40px;
        margin-right: 10px;
      }
      
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #409EFF;
        margin: 0;
      }
    }
    
    .header-right {
        display: flex;
        align-items: center;
        
        .notification-icon {
          margin-right: 20px;
          cursor: pointer;
          padding: 8px;
          border-radius: 50%;
          transition: all 0.3s;
          color: #409EFF; // 明确设置图标颜色

          &:hover {
            background-color: #f5f5f5;
            color: #337ecc; // 悬停时稍微深一点的颜色
          }

          // 确保图标在不同背景下都清晰可见
          .el-icon {
            color: inherit;
            font-size: 20px;
          }

          // 未读消息徽章样式优化
          :deep(.el-badge__content) {
            background-color: #f56c6c;
            border: 2px solid #fff;
            font-size: 12px;
            font-weight: 600;
            min-width: 18px;
            height: 18px;
            line-height: 14px;
          }
        }
        
        /* 统一聊天窗口样式已移至UnifiedChatWindow组件 */
        
        .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .merchant-name {
          margin: 0 8px;
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
  
  .merchant-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .merchant-sidebar {
      width: 230px;
      height: 100%;
      position: relative;
      background-color: #fff;
      transition: width 0.3s;
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
      z-index: 5;
      
      &.collapse {
        width: 64px;
      }
      
      .sidebar-menu {
        height: calc(100% - 40px);
        border-right: none;
      }
      
      .sidebar-toggle {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-top: 1px solid #e0e0e0;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
    
    .merchant-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background-color: #f5f7fa;
      
      .breadcrumb {
        padding: 16px 20px;
        background-color: #fff;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .content-container {
        flex: 1;
        padding: 20px;
        overflow: auto;
      }
    }
  }
}

/* 移除通知相关样式，聊天样式在MerchantChat组件中定义 */

/* 全局加载状态样式 */
.global-loading,
.page-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 999;
  
  .loading-icon,
  .is-loading {
    font-size: 42px;
    color: #409EFF;
    margin-bottom: 16px;
    animation: rotate 2s linear infinite;
  }
  
  p {
    font-size: 16px;
    color: #606266;
  }
  
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>